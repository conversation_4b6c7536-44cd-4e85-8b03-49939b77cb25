init python:
    debug_visible = False  # Tracks if debug screen is visible
    inventory_closing = False

    def show_inventory_with_animation():
        """Show inventory screen with entrance animation"""
        global inventory_closing
        if renpy.get_screen("inventory") is None:
            inventory_closing = False
            renpy.show_screen("inventory")

    def close_inventory_with_animation():
        """Close inventory screen with exit animation"""
        global inventory_closing
        if renpy.get_screen("inventory") is not None:
            inventory_closing = True
            renpy.restart_interaction()

init python:
    # Player health for debug/demo
    player_health = 100

    # Inventory items list
    inventory_items = [
        {"name": "Health Potion", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "<PERSON><PERSON> Potion", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Elixir", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Bomb", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Key", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Scroll", "image": "gui/inventory/item_idle.png", "available": True},
    ]

    def consume_item(index):
        """
        Consume the item at given index.
        Marks it unavailable and reduces player health for demo.
        """
        global player_health
        if 0 <= index < len(inventory_items) and inventory_items[index]["available"]:
            inventory_items[index]["available"] = False
            player_health = max(0, player_health - 10)  # example effect: reduce health by 10
            renpy.notify(f"Consumed {inventory_items[index]['name']}! Health is now {player_health}.")
        else:
            renpy.notify("Item not available or invalid index.")

    def add_item(name, image="gui/inventory/item_idle.png"):
        """
        Add a new item to inventory (for debug).
        """
        inventory_items.append({"name": name, "image": image, "available": True})
        renpy.notify(f"Added {name} to inventory.")

    def remove_item(index):
        """
        Remove item at index (for debug).
        """
        if 0 <= index < len(inventory_items):
            removed = inventory_items.pop(index)
            renpy.notify(f"Removed {removed['name']} from inventory.")
        else:
            renpy.notify("Invalid index for removal.")

# Inventory Animation Transforms
transform inventory_screen_enter:
    alpha 0.0
    easein 0.4 alpha 1.0

transform inventory_screen_exit:
    alpha 1.0
    easeout 0.3 alpha 0.0

transform inventory_header_enter:
    alpha 0.0 yoffset -20
    pause 0.1
    easein 0.3 alpha 1.0 yoffset 0

transform inventory_content_enter:
    alpha 0.0 yoffset 20
    pause 0.2
    easein 0.4 alpha 1.0 yoffset 0

transform inventory_buttons_enter:
    alpha 0.0 xoffset 20
    pause 0.3
    easein 0.3 alpha 1.0 xoffset 0

transform inventory_item_enter(delay=0.0):
    alpha 0.0 yoffset 15 zoom 0.9
    pause (0.4 + delay)
    easein 0.3 alpha 1.0 yoffset 0 zoom 1.0

transform inventory_item_hover:
    on idle:
        easein 0.2 alpha 1.0 zoom 1.0
    on hover:
        easein 0.2 alpha 0.9 zoom 1.05

transform inventory_debug_enter:
    alpha 0.0 xoffset -30
    pause 0.5
    easein 0.3 alpha 1.0 xoffset 0

transform confirm_dialog_enter:
    alpha 0.0 zoom 0.8
    easein 0.3 alpha 1.0 zoom 1.0

transform confirm_dialog_exit:
    alpha 1.0 zoom 1.0
    easeout 0.2 alpha 0.0 zoom 0.9

transform confirm_background_enter:
    alpha 0.0
    easein 0.3 alpha 1.0

transform confirm_background_exit:
    alpha 1.0
    easeout 0.2 alpha 0.0

transform inventory_button_fade:
    alpha 0.0
    easein 0.4 alpha 1.0

screen confirm_consume(index):
    add "gui/inventory/confirm.png" at confirm_background_enter
    modal True

    frame:
        xalign 0.5
        yalign 0.5
        padding (20, 20, 20, 20)
        background None
        at confirm_dialog_enter

        vbox:
            xalign 0.5
            spacing 20
            text "Consume [inventory_items[index]['name']]?" font "fonts/Montserrat-SemiBold.ttf" size 30

            hbox:
                ypos 67
                spacing 180
                xalign 0.5

                textbutton "Yes":
                    action [Function(consume_item, index), Hide("confirm_consume"), Show("inventory")]
                    at inventory_item_hover

                textbutton "No":
                    action Hide("confirm_consume")
                    at inventory_item_hover

screen inventory():
    key "game_menu" action Function(close_inventory_with_animation)
    key "K_ESCAPE" action Function(close_inventory_with_animation)

    if inventory_closing:
        timer 0.3 action Hide("inventory")

    add "gui/inventory/background.png" xpos 0.5 ypos 0.5 xanchor 0.5 yanchor 0.5 at (inventory_screen_exit if inventory_closing else inventory_screen_enter)

    frame:
        background "assets/settings/images/logo.png"
        xpos 853 ypos 260
        at (None if inventory_closing else inventory_header_enter)
    frame:
        background "assets/settings/images/logo.png"
        xpos 853 ypos 260
        xysize (214, 65)
        text _("INVENTORY") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"
        at (None if inventory_closing else inventory_header_enter)

    button:
        background 'assets/menu/images/report_button.svg'
        xysize (90, 34)
        xpos 0.5 ypos 0.73 xanchor 0.5
        text _("CLOSE") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
        action Function(close_inventory_with_animation)
        at (None if inventory_closing else inventory_button_fade)

    button:
        background 'assets/menu/images/report_button.svg'
        xysize (90, 34)
        text _("debug") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
        action If(debug_visible,
                [Hide("debug_inventory"), SetVariable("debug_visible", False)],
                [Show("debug_inventory"), SetVariable("debug_visible", True)])
        at (inventory_item_hover if inventory_closing else [inventory_item_hover, inventory_buttons_enter])

    frame:
        background None
        xpos 0.23 ypos 350
        xysize (1050, 400)
        at (None if inventory_closing else inventory_content_enter)

        viewport:
            mousewheel True
            draggable (False, True)
            scrollbars "vertical"
            xysize (1010, 650)

            grid 4 5:
                xspacing 15
                yspacing 15

                for i, item in enumerate(inventory_items):
                    if item["available"]:
                        frame:
                            background None
                            xysize (230, 157)
                            has vbox
                            spacing 5
                            align (0.5, 0.5)
                            at (inventory_item_hover if inventory_closing else [inventory_item_hover, inventory_item_enter(i * 0.05)])

                            imagebutton:
                                idle item["image"]
                                hover "gui/inventory/item_hover.png"
                                xsize 230 ysize 130
                                action Show("confirm_consume", index=i)

                            text item["name"]:
                                size 14
                                color "#fff"
                                font "fonts/Montserrat-SemiBold.ttf"
                                align (0.5, 0.5)

screen debug_inventory():
    frame:
        xalign 0.95
        yalign 0.05
        background "#0008"
        padding (20, 20, 20, 20)
        at inventory_debug_enter

        vbox:
            spacing 10
            text "DEBUG MENU" size 18 color "#fff"
            text "Player Health: [player_health]" size 16

            textbutton "Add Health Potion":
                action Function(add_item, "Health Potion")
                at inventory_item_hover

            textbutton "Add Mana Potion":
                action Function(add_item, "Mana Potion")
                at inventory_item_hover

            textbutton "Remove Last Item":
                action Function(remove_item, len(inventory_items) - 1)
                at inventory_item_hover

            textbutton "Heal Player +10":
                action [
                    SetVariable("player_health", min(100, player_health + 10)),
                    Function(renpy.notify, "Healed! Health is now [player_health].")
                ]
                at inventory_item_hover

            textbutton "Damage Player -10":
                action [
                    SetVariable("player_health", max(0, player_health - 10)),
                    Function(renpy.notify, "Damaged! Health is now [player_health].")
                ]
                at inventory_item_hover
